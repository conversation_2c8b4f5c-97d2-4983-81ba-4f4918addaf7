[build]
  command = "npm run build"
  publish = ".next"

[[plugins]]
  package = "@netlify/plugin-nextjs"

[build.environment]
  NEXT_PRIVATE_TARGET = "server"

# Redirect rules for SPA behavior
[[redirects]]
  from = "/*"
  to = "/404.html"
  status = 404

# API routes
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/nextjs-func/:splat"
  status = 200

[functions]
  directory = ".netlify/functions"

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'" 