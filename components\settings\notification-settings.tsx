'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { SettingCategory } from '@/types/settings';
import { Bell, Mail, Smartphone, MessageSquare, RotateCcw, Volume2, VolumeX } from 'lucide-react';

interface NotificationSettingsProps {
  getSetting: (category: SettingCategory, key: string) => any;
  updateSetting: (category: SettingCategory, key: string, value: any) => Promise<void>;
  onReset: () => void;
}

const frequencies = [
  { value: 'immediate', label: 'Immediately' },
  { value: 'hourly', label: 'Hourly digest' },
  { value: 'daily', label: 'Daily digest' },
  { value: 'weekly', label: 'Weekly digest' },
];

export function NotificationSettings({
  getSetting,
  updateSetting,
  onReset,
}: NotificationSettingsProps) {
  // Get current settings with defaults
  const notifications = getSetting('notifications', 'settings') || {
    email: {
      enabled: true,
      frequency: 'immediate',
      types: {
        orderUpdates: true,
        systemAlerts: true,
        marketing: false,
        security: true,
        teamInvitations: true,
        roleChanges: true,
      },
    },
    push: {
      enabled: false,
      types: {
        orderUpdates: true,
        systemAlerts: true,
        teamNotifications: true,
      },
    },
    sms: {
      enabled: false,
      types: {
        criticalAlerts: true,
        orderUpdates: false,
      },
    },
  };

  const updateEmailSetting = async (field: string, value: any) => {
    await updateSetting('notifications', 'settings', {
      ...notifications,
      email: {
        ...notifications.email,
        [field]: value,
      },
    });
  };

  const updateEmailType = async (type: string, enabled: boolean) => {
    await updateSetting('notifications', 'settings', {
      ...notifications,
      email: {
        ...notifications.email,
        types: {
          ...notifications.email.types,
          [type]: enabled,
        },
      },
    });
  };

  const updatePushSetting = async (field: string, value: any) => {
    await updateSetting('notifications', 'settings', {
      ...notifications,
      push: {
        ...notifications.push,
        [field]: value,
      },
    });
  };

  const updatePushType = async (type: string, enabled: boolean) => {
    await updateSetting('notifications', 'settings', {
      ...notifications,
      push: {
        ...notifications.push,
        types: {
          ...notifications.push.types,
          [type]: enabled,
        },
      },
    });
  };

  const updateSmsSetting = async (field: string, value: any) => {
    await updateSetting('notifications', 'settings', {
      ...notifications,
      sms: {
        ...notifications.sms,
        [field]: value,
      },
    });
  };

  const updateSmsType = async (type: string, enabled: boolean) => {
    await updateSetting('notifications', 'settings', {
      ...notifications,
      sms: {
        ...notifications.sms,
        types: {
          ...notifications.sms.types,
          [type]: enabled,
        },
      },
    });
  };

  return (
    <div className="space-y-6">
      {/* Email Notifications */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Email Notifications
              </CardTitle>
              <CardDescription>
                Configure when and how you receive email notifications.
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Switch
                checked={notifications.email.enabled}
                onCheckedChange={(checked) => updateEmailSetting('enabled', checked)}
              />
              <Button variant="outline" size="sm" onClick={onReset}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Email Frequency */}
          <div className="space-y-2">
            <Label>Email Frequency</Label>
            <Select
              value={notifications.email.frequency}
              onValueChange={(value) => updateEmailSetting('frequency', value)}
              disabled={!notifications.email.enabled}
            >
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {frequencies.map((freq) => (
                  <SelectItem key={freq.value} value={freq.value}>
                    {freq.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Email Types */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Email Types</Label>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="font-normal">Order Updates</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications about order status changes and updates
                  </p>
                </div>
                <Switch
                  checked={notifications.email.types.orderUpdates}
                  onCheckedChange={(checked) => updateEmailType('orderUpdates', checked)}
                  disabled={!notifications.email.enabled}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Label className="font-normal">System Alerts</Label>
                    <Badge variant="secondary" className="text-xs">Important</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Critical system notifications and maintenance updates
                  </p>
                </div>
                <Switch
                  checked={notifications.email.types.systemAlerts}
                  onCheckedChange={(checked) => updateEmailType('systemAlerts', checked)}
                  disabled={!notifications.email.enabled}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Label className="font-normal">Security Alerts</Label>
                    <Badge variant="destructive" className="text-xs">Critical</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Login attempts, password changes, and security events
                  </p>
                </div>
                <Switch
                  checked={notifications.email.types.security}
                  onCheckedChange={(checked) => updateEmailType('security', checked)}
                  disabled={!notifications.email.enabled}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="font-normal">Team Invitations</Label>
                  <p className="text-sm text-muted-foreground">
                    Invitations to join teams and role assignments
                  </p>
                </div>
                <Switch
                  checked={notifications.email.types.teamInvitations}
                  onCheckedChange={(checked) => updateEmailType('teamInvitations', checked)}
                  disabled={!notifications.email.enabled}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="font-normal">Role Changes</Label>
                  <p className="text-sm text-muted-foreground">
                    Changes to your permissions and role assignments
                  </p>
                </div>
                <Switch
                  checked={notifications.email.types.roleChanges}
                  onCheckedChange={(checked) => updateEmailType('roleChanges', checked)}
                  disabled={!notifications.email.enabled}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="font-normal">Marketing & Updates</Label>
                  <p className="text-sm text-muted-foreground">
                    Product updates, newsletters, and promotional content
                  </p>
                </div>
                <Switch
                  checked={notifications.email.types.marketing}
                  onCheckedChange={(checked) => updateEmailType('marketing', checked)}
                  disabled={!notifications.email.enabled}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Push Notifications */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Smartphone className="h-5 w-5" />
                Push Notifications
              </CardTitle>
              <CardDescription>
                Receive instant notifications on your devices.
              </CardDescription>
            </div>
            <Switch
              checked={notifications.push.enabled}
              onCheckedChange={(checked) => updatePushSetting('enabled', checked)}
            />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="font-normal">Order Updates</Label>
                <p className="text-sm text-muted-foreground">
                  Real-time order status notifications
                </p>
              </div>
              <Switch
                checked={notifications.push.types.orderUpdates}
                onCheckedChange={(checked) => updatePushType('orderUpdates', checked)}
                disabled={!notifications.push.enabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="font-normal">System Alerts</Label>
                <p className="text-sm text-muted-foreground">
                  Important system notifications
                </p>
              </div>
              <Switch
                checked={notifications.push.types.systemAlerts}
                onCheckedChange={(checked) => updatePushType('systemAlerts', checked)}
                disabled={!notifications.push.enabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="font-normal">Team Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Messages and updates from your team
                </p>
              </div>
              <Switch
                checked={notifications.push.types.teamNotifications}
                onCheckedChange={(checked) => updatePushType('teamNotifications', checked)}
                disabled={!notifications.push.enabled}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* SMS Notifications */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                SMS Notifications
              </CardTitle>
              <CardDescription>
                Receive critical alerts via text message.
              </CardDescription>
            </div>
            <Switch
              checked={notifications.sms.enabled}
              onCheckedChange={(checked) => updateSmsSetting('enabled', checked)}
            />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <Label className="font-normal">Critical Alerts</Label>
                  <Badge variant="destructive" className="text-xs">Critical</Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  Security breaches and system failures
                </p>
              </div>
              <Switch
                checked={notifications.sms.types.criticalAlerts}
                onCheckedChange={(checked) => updateSmsType('criticalAlerts', checked)}
                disabled={!notifications.sms.enabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="font-normal">Order Updates</Label>
                <p className="text-sm text-muted-foreground">
                  Important order status changes
                </p>
              </div>
              <Switch
                checked={notifications.sms.types.orderUpdates}
                onCheckedChange={(checked) => updateSmsType('orderUpdates', checked)}
                disabled={!notifications.sms.enabled}
              />
            </div>
          </div>

          {notifications.sms.enabled && (
            <div className="mt-4 p-3 bg-amber-50 dark:bg-amber-950 border border-amber-200 dark:border-amber-800 rounded-md">
              <p className="text-sm text-amber-800 dark:text-amber-200">
                <strong>Note:</strong> SMS notifications may incur charges based on your mobile plan. 
                We recommend enabling only critical alerts to minimize costs.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 