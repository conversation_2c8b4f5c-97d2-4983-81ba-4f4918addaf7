{"name": "restaurant-saas-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "build:netlify": "npm run build && npm run export", "export": "next export"}, "dependencies": {"@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@stripe/stripe-js": "^3.0.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.3.1", "framer-motion": "^11.0.8", "lucide-react": "^0.344.0", "next": "14.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.57.0", "stripe": "^14.21.0", "zod": "^3.25.51"}, "devDependencies": {"@netlify/plugin-nextjs": "^5.2.0", "@types/node": "^20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-config-next": "14.2.0", "postcss": "^8.5.4", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3"}}