'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Separator } from '@/components/ui/separator';
import { SettingCategory } from '@/types/settings';
import { Palette, Monitor, Sun, Moon, Smartphone, RotateCcw, Layout, Type, Zap } from 'lucide-react';

interface AppearanceSettingsProps {
  getSetting: (category: SettingCategory, key: string) => any;
  updateSetting: (category: SettingCategory, key: string, value: any) => Promise<void>;
  onReset: () => void;
}

const themes = [
  { value: 'light', label: 'Light', icon: Sun },
  { value: 'dark', label: 'Dark', icon: Moon },
  { value: 'system', label: 'System', icon: Monitor },
];

const densities = [
  { value: 'compact', label: 'Compact', description: 'More content, less spacing' },
  { value: 'comfortable', label: 'Comfortable', description: 'Balanced spacing' },
  { value: 'spacious', label: 'Spacious', description: 'More spacing, easier to read' },
];

const fontFamilies = [
  { value: 'Inter', label: 'Inter (Default)' },
  { value: 'system-ui', label: 'System UI' },
  { value: 'Arial', label: 'Arial' },
  { value: 'Helvetica', label: 'Helvetica' },
  { value: 'Georgia', label: 'Georgia' },
  { value: 'Times New Roman', label: 'Times New Roman' },
  { value: 'Courier New', label: 'Courier New' },
];

const accentColors = [
  { value: '#3b82f6', label: 'Blue', color: 'bg-blue-500' },
  { value: '#10b981', label: 'Green', color: 'bg-green-500' },
  { value: '#f59e0b', label: 'Amber', color: 'bg-amber-500' },
  { value: '#ef4444', label: 'Red', color: 'bg-red-500' },
  { value: '#8b5cf6', label: 'Purple', color: 'bg-purple-500' },
  { value: '#06b6d4', label: 'Cyan', color: 'bg-cyan-500' },
  { value: '#ec4899', label: 'Pink', color: 'bg-pink-500' },
  { value: '#84cc16', label: 'Lime', color: 'bg-lime-500' },
];

export function AppearanceSettings({
  getSetting,
  updateSetting,
  onReset,
}: AppearanceSettingsProps) {
  // Get current settings with defaults
  const appearance = getSetting('appearance', 'settings') || {
    theme: 'system',
    density: 'comfortable',
    sidebar: {
      collapsed: false,
      position: 'left',
      pinned: true,
    },
    animations: {
      enabled: true,
      reducedMotion: false,
    },
    customization: {
      accentColor: '#3b82f6',
      fontFamily: 'Inter',
      fontSize: 14,
    },
  };

  const updateAppearance = async (field: string, value: any) => {
    await updateSetting('appearance', 'settings', {
      ...appearance,
      [field]: value,
    });
  };

  const updateSidebar = async (field: string, value: any) => {
    await updateSetting('appearance', 'settings', {
      ...appearance,
      sidebar: {
        ...appearance.sidebar,
        [field]: value,
      },
    });
  };

  const updateAnimations = async (field: string, value: any) => {
    await updateSetting('appearance', 'settings', {
      ...appearance,
      animations: {
        ...appearance.animations,
        [field]: value,
      },
    });
  };

  const updateCustomization = async (field: string, value: any) => {
    await updateSetting('appearance', 'settings', {
      ...appearance,
      customization: {
        ...appearance.customization,
        [field]: value,
      },
    });
  };

  return (
    <div className="space-y-6">
      {/* Theme Selection */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Theme
              </CardTitle>
              <CardDescription>
                Choose your preferred color scheme.
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={onReset}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-3">
            {themes.map((theme) => {
              const Icon = theme.icon;
              return (
                <button
                  key={theme.value}
                  onClick={() => updateAppearance('theme', theme.value)}
                  className={`p-4 border rounded-lg text-center transition-colors hover:bg-accent ${
                    appearance.theme === theme.value
                      ? 'border-primary bg-primary/5'
                      : 'border-border'
                  }`}
                >
                  <Icon className="h-6 w-6 mx-auto mb-2" />
                  <div className="font-medium">{theme.label}</div>
                </button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Layout & Density */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layout className="h-5 w-5" />
            Layout & Density
          </CardTitle>
          <CardDescription>
            Configure the layout and spacing of interface elements.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Density */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Interface Density</Label>
            <div className="grid grid-cols-1 gap-3">
              {densities.map((density) => (
                <button
                  key={density.value}
                  onClick={() => updateAppearance('density', density.value)}
                  className={`p-3 border rounded-lg text-left transition-colors hover:bg-accent ${
                    appearance.density === density.value
                      ? 'border-primary bg-primary/5'
                      : 'border-border'
                  }`}
                >
                  <div className="font-medium">{density.label}</div>
                  <div className="text-sm text-muted-foreground">{density.description}</div>
                </button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Sidebar Settings */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Sidebar</Label>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-normal">Collapsed by default</Label>
                  <p className="text-sm text-muted-foreground">
                    Start with the sidebar collapsed
                  </p>
                </div>
                <Switch
                  checked={appearance.sidebar.collapsed}
                  onCheckedChange={(checked) => updateSidebar('collapsed', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-normal">Pin sidebar</Label>
                  <p className="text-sm text-muted-foreground">
                    Keep sidebar visible when navigating
                  </p>
                </div>
                <Switch
                  checked={appearance.sidebar.pinned}
                  onCheckedChange={(checked) => updateSidebar('pinned', checked)}
                />
              </div>

              <div className="space-y-2">
                <Label>Sidebar Position</Label>
                <Select
                  value={appearance.sidebar.position}
                  onValueChange={(value) => updateSidebar('position', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="left">Left</SelectItem>
                    <SelectItem value="right">Right</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Animations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Animations & Motion
          </CardTitle>
          <CardDescription>
            Control interface animations and motion effects.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="font-normal">Enable animations</Label>
              <p className="text-sm text-muted-foreground">
                Show smooth transitions and animations
              </p>
            </div>
            <Switch
              checked={appearance.animations.enabled}
              onCheckedChange={(checked) => updateAnimations('enabled', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="font-normal">Reduced motion</Label>
              <p className="text-sm text-muted-foreground">
                Minimize motion for accessibility
              </p>
            </div>
            <Switch
              checked={appearance.animations.reducedMotion}
              onCheckedChange={(checked) => updateAnimations('reducedMotion', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Customization */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Type className="h-5 w-5" />
            Customization
          </CardTitle>
          <CardDescription>
            Personalize colors, fonts, and sizing.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Accent Color */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Accent Color</Label>
            <div className="grid grid-cols-4 gap-2">
              {accentColors.map((color) => (
                <button
                  key={color.value}
                  onClick={() => updateCustomization('accentColor', color.value)}
                  className={`p-3 border rounded-lg text-center transition-colors hover:bg-accent ${
                    appearance.customization.accentColor === color.value
                      ? 'border-primary bg-primary/5'
                      : 'border-border'
                  }`}
                >
                  <div className={`w-6 h-6 rounded-full mx-auto mb-1 ${color.color}`} />
                  <div className="text-xs">{color.label}</div>
                </button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Font Family */}
          <div className="space-y-2">
            <Label>Font Family</Label>
            <Select
              value={appearance.customization.fontFamily}
              onValueChange={(value) => updateCustomization('fontFamily', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {fontFamilies.map((font) => (
                  <SelectItem key={font.value} value={font.value}>
                    {font.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Font Size */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Font Size</Label>
              <span className="text-sm text-muted-foreground">
                {appearance.customization.fontSize}px
              </span>
            </div>
            <Slider
              value={[appearance.customization.fontSize]}
              onValueChange={(value) => updateCustomization('fontSize', value[0])}
              min={12}
              max={18}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Small (12px)</span>
              <span>Large (18px)</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 