'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { SettingCategory } from '@/types/settings';
import { Shield, Eye, Database, Clock, RotateCcw, AlertTriangle } from 'lucide-react';

interface PrivacySettingsProps {
  getSetting: (category: SettingCategory, key: string) => any;
  updateSetting: (category: SettingCategory, key: string, value: any) => Promise<void>;
  onReset: () => void;
}

const visibilityOptions = [
  { value: 'public', label: 'Public', description: 'Visible to everyone' },
  { value: 'organization', label: 'Organization', description: 'Visible to organization members only' },
  { value: 'private', label: 'Private', description: 'Visible only to you' },
];

export function PrivacySettings({
  getSetting,
  updateSetting,
  onReset,
}: PrivacySettingsProps) {
  // Get current settings with defaults
  const privacy = getSetting('privacy', 'settings') || {
    profileVisibility: 'organization',
    dataSharing: {
      analytics: true,
      marketing: false,
      thirdParty: false,
    },
    sessionSettings: {
      rememberMe: true,
      sessionTimeout: 480, // 8 hours in minutes
      logoutOnClose: false,
    },
  };

  const updatePrivacy = async (field: string, value: any) => {
    await updateSetting('privacy', 'settings', {
      ...privacy,
      [field]: value,
    });
  };

  const updateDataSharing = async (field: string, value: any) => {
    await updateSetting('privacy', 'settings', {
      ...privacy,
      dataSharing: {
        ...privacy.dataSharing,
        [field]: value,
      },
    });
  };

  const updateSessionSettings = async (field: string, value: any) => {
    await updateSetting('privacy', 'settings', {
      ...privacy,
      sessionSettings: {
        ...privacy.sessionSettings,
        [field]: value,
      },
    });
  };

  const formatSessionTimeout = (minutes: number) => {
    if (minutes < 60) return `${minutes} minutes`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
    return `${hours}h ${remainingMinutes}m`;
  };

  return (
    <div className="space-y-6">
      {/* Profile Visibility */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Profile Visibility
              </CardTitle>
              <CardDescription>
                Control who can see your profile information.
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={onReset}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {visibilityOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => updatePrivacy('profileVisibility', option.value)}
                className={`w-full p-3 border rounded-lg text-left transition-colors hover:bg-accent ${
                  privacy.profileVisibility === option.value
                    ? 'border-primary bg-primary/5'
                    : 'border-border'
                }`}
              >
                <div className="font-medium">{option.label}</div>
                <div className="text-sm text-muted-foreground">{option.description}</div>
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Data Sharing */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Data Sharing & Analytics
          </CardTitle>
          <CardDescription>
            Choose what data you're comfortable sharing to improve our services.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">Usage Analytics</Label>
              <p className="text-sm text-muted-foreground">
                Help us improve the platform by sharing anonymous usage data
              </p>
            </div>
            <Switch
              checked={privacy.dataSharing.analytics}
              onCheckedChange={(checked) => updateDataSharing('analytics', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">Marketing Communications</Label>
              <p className="text-sm text-muted-foreground">
                Receive personalized product recommendations and updates
              </p>
            </div>
            <Switch
              checked={privacy.dataSharing.marketing}
              onCheckedChange={(checked) => updateDataSharing('marketing', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Label className="font-normal">Third-party Integrations</Label>
                <Badge variant="outline" className="text-xs">Advanced</Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                Allow integrated services to access your data for enhanced functionality
              </p>
            </div>
            <Switch
              checked={privacy.dataSharing.thirdParty}
              onCheckedChange={(checked) => updateDataSharing('thirdParty', checked)}
            />
          </div>

          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-md">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>Privacy Note:</strong> We never sell your personal data. All shared data is anonymized 
              and used solely to improve our services. You can change these settings at any time.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Session Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Session & Security
          </CardTitle>
          <CardDescription>
            Configure how long you stay logged in and session behavior.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">Remember me</Label>
              <p className="text-sm text-muted-foreground">
                Stay logged in across browser sessions
              </p>
            </div>
            <Switch
              checked={privacy.sessionSettings.rememberMe}
              onCheckedChange={(checked) => updateSessionSettings('rememberMe', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="font-normal">Logout when browser closes</Label>
              <p className="text-sm text-muted-foreground">
                Automatically sign out when you close your browser
              </p>
            </div>
            <Switch
              checked={privacy.sessionSettings.logoutOnClose}
              onCheckedChange={(checked) => updateSessionSettings('logoutOnClose', checked)}
            />
          </div>

          <Separator />

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Session Timeout</Label>
              <span className="text-sm text-muted-foreground">
                {formatSessionTimeout(privacy.sessionSettings.sessionTimeout)}
              </span>
            </div>
            <Slider
              value={[privacy.sessionSettings.sessionTimeout]}
              onValueChange={(value) => updateSessionSettings('sessionTimeout', value[0])}
              min={30}
              max={1440} // 24 hours
              step={30}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>30 minutes</span>
              <span>24 hours</span>
            </div>
            <p className="text-sm text-muted-foreground">
              You'll be automatically logged out after this period of inactivity.
            </p>
          </div>

          {privacy.sessionSettings.sessionTimeout < 120 && (
            <div className="p-3 bg-amber-50 dark:bg-amber-950 border border-amber-200 dark:border-amber-800 rounded-md">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5" />
                <p className="text-sm text-amber-800 dark:text-amber-200">
                  <strong>Security Warning:</strong> Short session timeouts may impact your productivity. 
                  Consider using at least 2 hours for better user experience.
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 